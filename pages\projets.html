<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RedFox – Projets</title>
    <link rel="stylesheet" href="../style.css" />
  </head>
  <body>
    <nav class="navbar">
      <button
        class="sidebar-toggle"
        aria-label="Ouvrir/fermer le menu"
        aria-controls="sidebar-definitions"
        aria-expanded="false"
      >
        <span class="hamburger"><span></span><span></span><span></span></span>
      </button>
      <ul>
        <li><a href="../index.html#accueil">Accueil</a></li>
        <li><a href="../index.html#a-propos">À propos</a></li>
        <li><a href="../index.html#competences">Compétences</a></li>
        <li><a href="../index.html#projets">Projets</a></li>
        <li><a href="../index.html#certifications">Certifications</a></li>
        <li><a href="../index.html#contact">Contact</a></li>
      </ul>
      <button id="theme-toggle" class="theme-toggle" aria-label="Changer de thème" title="Changer de thème">
        <span class="theme-icon">🌙</span>
      </button>
    </nav>

    <!-- CTA pour encourager l'ouverture du sidebar -->
    <div class="sidebar-cta" id="sidebar-cta" aria-hidden="true">
      <div class="cta-content">
        <span class="cta-arrow">←</span>
        <span class="cta-text">Découvrez mon expérience</span>
      </div>
    </div>

    <aside class="sidebar-definitions" id="sidebar-definitions">
      <h2>Expérience</h2>
      <ul class="companies-list">
        <li class="company-item">BNP Paribas / Montreuil</li>
        <li class="company-item">Orange Groupe / Bagnolet</li>
        <li class="company-item">Orange Business / Nantes</li>
      </ul>
    </aside>

    <section class="section projets-details">
      <h1>Projets</h1>

      <!-- Filtres des projets -->
      <div class="projects-filters" role="region" aria-label="Filtres des projets">
        <div class="filter-search">
          <label for="project-search" class="sr-only">Rechercher un projet</label>
          <input
            type="text"
            id="project-search"
            class="search-input"
            placeholder="Rechercher un projet..."
            aria-describedby="search-help"
          />
          <span id="search-help" class="sr-only">Tapez pour filtrer les projets par nom ou description</span>
        </div>

        <div class="filter-results" aria-live="polite" aria-atomic="true">
          <span id="results-count">3 projets affichés</span>
        </div>
      </div>

      <div class="projects-grid">
        <!-- Placeholder for FoxGen -->
        <div class="project-card" data-tags="Python,IA Générative,Pixel Art" data-search-text="foxgen générateur images ia pixel art faune sauvage">
          <h2>FoxGen</h2>
          <div class="project-placeholder-image"></div>
          <p>Générateur d'images IA inspiré du pixel art et de la faune sauvage.</p>
          <div class="project-tech">
            <span class="tech-tag">Python</span>
            <span class="tech-tag">IA Générative</span>
            <span class="tech-tag">Pixel Art</span>
          </div>
          <a href="#" class="project-link">Voir le projet</a>
        </div>

        <!-- Placeholder for Text2Quest -->
        <div class="project-card" data-tags="JavaScript,IA Générative,UX Design" data-search-text="text2quest aventures textuelles interactives ia générative jeu ux design">
          <h2>Text2Quest</h2>
          <div class="project-placeholder-image"></div>
          <p>Création d'aventures textuelles interactives grâce à l'IA générative.</p>
          <div class="project-tech">
            <span class="tech-tag">JavaScript</span>
            <span class="tech-tag">IA Générative</span>
            <span class="tech-tag">UX Design</span>
          </div>
          <a href="#" class="project-link">Voir le projet</a>
        </div>

        <!-- Placeholder for RetroSynth -->
        <div class="project-card" data-tags="JavaScript,Web Audio API,IA Générative" data-search-text="retrosynth synthétiseur sons rétro ia jeux vidéo pixel art audio">
          <h2>RetroSynth</h2>
          <div class="project-placeholder-image"></div>
          <p>Synthétiseur de sons rétro piloté par IA pour jeux vidéo pixel art.</p>
          <div class="project-tech">
            <span class="tech-tag">JavaScript</span>
            <span class="tech-tag">Web Audio API</span>
            <span class="tech-tag">IA Générative</span>
          </div>
          <a href="#" class="project-link">Voir le projet</a>
        </div>
      </div>
    </section>

    <footer class="footer">
      <p>&copy; 2025 RedFox. Tous droits réservés.</p>
      <p class="datetime"></p>
    </footer>

    <script src="../script.js" defer></script>
  </body>
</html>